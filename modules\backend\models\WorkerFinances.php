<?php

namespace app\modules\backend\models;

use Yii;

/**
 * This is the model class for table "worker_finances".
 *
 * @property int $id
 * @property int $worker_id
 * @property string $month
 * @property string $type
 * @property float $amount
 * @property string|null $description
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Worker $worker
 */
class WorkerFinances extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'worker_finances';
    }

        
    const TYPE_SALARY = 1;

    const TYPE_ADVANCE = 2;

    const TYPE_BONUS = 3;
    const TYPE_DEBT = 4;
    const TYPE_ONE_TIME_PAYMENT = 5;
    const TYPE_DEBT_PAYMENT = 6;
    const TYPE_VACATION_PAY = 7;
    const TYPE_CASH_SALARY = 8;
    const TYPE_CASH_ADVANCE = 9;
    const TYPE_CASH_BONUS = 10;
    const TYPE_CASH_ONE_TIME_PAYMENT = 11;
    const TYPE_CASH_VACATION_PAY = 12;


    public static function getTypes()
    {
        return [
            self::TYPE_SALARY => Yii::t('app', 'salary'),
            self::TYPE_ADVANCE => Yii::t('app', 'advance'),
            self::TYPE_BONUS => Yii::t('app', 'bonus'),
            self::TYPE_DEBT => Yii::t('app', 'debt'),
            self::TYPE_ONE_TIME_PAYMENT => Yii::t('app', 'one_time_payment'),
            self::TYPE_DEBT_PAYMENT => Yii::t('app', 'debt_payment'),
            self::TYPE_VACATION_PAY => Yii::t('app', 'vacation_pay'),
            self::TYPE_CASH_SALARY => Yii::t('app', 'cash_salary'),
            self::TYPE_CASH_ADVANCE => Yii::t('app', 'cash_advance'),
            self::TYPE_CASH_BONUS => Yii::t('app', 'cash_bonus'),
            self::TYPE_CASH_ONE_TIME_PAYMENT => Yii::t('app', 'cash_one_time_payment'),
            self::TYPE_CASH_VACATION_PAY => Yii::t('app', 'cash_vacation_pay'),
        ];
    }
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['worker_id', 'month', 'type', 'amount'], 'required'],
            [['worker_id', 'type'], 'integer'],
            [['amount'], 'number'],
            [['description'], 'string'],
            [['created_at', 'deleted_at'], 'safe'],
            [['month'], 'string', 'max' => 10],
            [['worker_id'], 'exist', 'skipOnError' => true, 'targetClass' => Worker::class, 'targetAttribute' => ['worker_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'worker_id' => Yii::t('app', 'worker'),
            'month' => Yii::t('app', 'month'),
            'type' => Yii::t('app', 'type'),
            'amount' => Yii::t('app', 'amount'),
            'payment_type' => Yii::t('app', 'payment type'),
            'description' => Yii::t('app', 'description'),
            'created_at' => Yii::t('app', 'created at'),
            'deleted_at' => Yii::t('app', 'deleted at'),
        ];
    }

    /**
     * Gets query for [[Worker]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getWorker()
    {
        return $this->hasOne(Worker::class, ['id' => 'worker_id']);
    }
}
