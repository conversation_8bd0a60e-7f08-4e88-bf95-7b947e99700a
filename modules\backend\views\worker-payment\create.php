<?php
use app\common\models\PaymentType;
use app\modules\backend\models\WorkerFinances;
use yii\helpers\Html;
use app\assets\Select2Asset;

Select2Asset::register($this);
?>

<style>
.payment-methods-container {
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.payment-methods-container .form-check {
    margin-bottom: 0;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 120px;
    position: relative;
}

.payment-methods-container .form-check:hover {
    border-color: #007bff;
    box-shadow: 0 1px 3px rgba(0,123,255,0.1);
}

.payment-methods-container .form-check-input {
    margin: 0;
    transform: scale(1.1);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

.payment-methods-container .form-check-label {
    font-weight: 500;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    margin: 0;
    white-space: nowrap;
    text-align: left;
    padding-right: 24px;
}

.payment-methods-container .form-check-input:disabled + .form-check-label {
    opacity: 0.5;
    cursor: not-allowed;
}

.payment-methods-container .form-check-input:checked + .form-check-label {
    color: #007bff;
    font-weight: 600;
}

/* Стили для основных чекбоксов типов платежей */
.payment-type-row .form-check {
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 150px;
    position: relative;
}

.payment-type-row .form-check:hover {
    background: #e3f2fd;
    border-color: #2196f3;
}

.payment-type-row .form-check-input {
    margin: 0;
    transform: scale(1.2);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

.payment-type-row .form-check-label {
    font-weight: 600;
    font-size: 15px;
    color: #343a40;
    margin: 0;
    white-space: nowrap;
    text-align: left;
    padding-right: 24px;
}

.payment-type-row .form-check-input:checked + .form-check-label {
    color:hsl(207, 89.70%, 54.10%);
}

/* Выравнивание контента в ячейках таблицы */
.payment-type-row td {
    vertical-align: middle;
}

.amount-column {
    vertical-align: middle;
}

/* Стили для полей ввода чисел */
input[inputmode="numeric"] {
    text-align: right;
}

/* Стили для секции погашения долга */
#debt-payment-section .card {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

#debt-payment-section .card-header {
    background:rgb(160, 190, 239);
    border-bottom: 1px solid #dee2e6;
    padding: 12px 15px;
}

#debt-payment-section .form-check {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    position: relative;
    cursor: pointer;
}

#debt-payment-section .form-check:hover {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 8px 12px;
    margin: -8px -12px;
}

#debt-payment-section .form-check-input {
    margin: 0;
    transform: scale(1.2);
    flex-shrink: 0;
    position: absolute;
    right: 10px;
}

#debt-payment-section .form-check-label {
    font-weight: 350;
    font-size: 15px;
    color:rgb(2, 11, 20);
    margin: 0;
    cursor: pointer;
    padding-right: 30px;
    width: 100%;
}

#debt-payment-section .form-check-input:checked + .form-check-label {
    color: #dc3545;
}

#debt-payment-section .card-body {
    background: white;
    padding: 20px;
}

/* Стили для валидации */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.error-container {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.validation-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.validation-warning {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

/* Стили для панели итогов */
#payment-summary .card-header {
    background: linear-gradient(45deg, #17a2b8, #138496) !important;
}

#payment-summary .card-body {
    background: #f8f9fa;
}

/* Анимация для полей с ошибками */
.is-invalid {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Стили для кнопок */
.btn-lg {
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #545b62);
    border: none;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #545b62, #3d4142);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Улучшенные стили для алертов */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}
</style>

<div class="worker-payment-form">
    <form id="worker-payment-create-form">
        
        <div class="row mb-3">
            <div class="col-md-8">
                <label for="worker_id"><?= Yii::t('app', 'worker') ?></label>
                <select id="worker_id" name="worker_id" class="form-control select2" required>
                    <option value=""><?= Yii::t('app', 'select_worker') ?></option>
                    <?php foreach ($workers as $worker): ?>
                        <option value="<?= $worker['id'] ?>"><?= Html::encode($worker['full_name']) ?></option>
                    <?php endforeach; ?>
                </select>
                <div class="error-container" id="worker_id-error"></div>
            </div>
            <div class="col-md-4">
                <label for="month"><?= Yii::t('app', 'month') ?></label>
                <input type="month" id="month" name="month" class="form-control" value="<?= date('Y-m') ?>" required>
                <div class="error-container" id="month-error"></div>
            </div>
        </div>

        <!-- Worker Info Panel -->
        <div id="worker-info" class="alert alert-info mt-3" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'salary') ?>:</strong> <span id="worker-salary">0</span>
                </div>
                <div class="col-md-4">
                    <strong><?= Yii::t('app', 'total_paid') ?>:</strong> <span id="worker-total-paid">0</span>
                </div>
                <div class="col-md-4" id="remaining-salary-info" style="display: none;">
                    <strong><?= Yii::t('app', 'remaining_salary') ?>:</strong> <span id="worker-remaining-salary">0</span>
                </div>
            </div>
        </div>

        <!-- Payment Types Selection -->
        <div class="form-group">
            <label><?= Yii::t('app', 'payment_types') ?></label>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th width="40%"><?= Yii::t('app', 'payment_type') ?></th>
                            <th width="30%"><?= Yii::t('app', 'payment_method') ?></th>
                            <th width="30%"><?= Yii::t('app', 'amount') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($paymentTypes as $typeId => $typeName): ?>
                            <?php if ($typeId != WorkerFinances::TYPE_DEBT_PAYMENT): // Исключаем долг из основной таблицы ?>
                                <tr class="payment-type-row" data-type="<?= $typeId ?>">
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input payment-type-checkbox" type="checkbox" 
                                                   id="payment_type_<?= $typeId ?>" value="<?= $typeId ?>">
                                            <label class="form-check-label" for="payment_type_<?= $typeId ?>">
                                                <?= $typeName ?>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="payment-methods-container">
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox" 
                                                       id="cash_<?= $typeId ?>" value="<?= PaymentType::CASH ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" disabled>
                                                <label class="form-check-label" for="cash_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'cash') ?>
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input payment-method-checkbox" type="checkbox" 
                                                       id="card_<?= $typeId ?>" value="<?= PaymentType::PAYMENT_CARD ?>"
                                                       name="payment_types[<?= $typeId ?>][methods][]" disabled>
                                                <label class="form-check-label" for="card_<?= $typeId ?>">
                                                    <?= Yii::t('app', 'payment_card') ?>
                                                </label>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="amount-column">
                                        <input type="text"
                                               class="form-control amount-input"
                                               name="payment_types[<?= $typeId ?>][amount]"
                                               placeholder="<?= Yii::t('app', 'amount') ?>"
                                               inputmode="numeric"
                                               pattern="[0-9\s]*"
                                               disabled>
                                        <div class="dynamic-amounts" style="display: none;">
                                            <!-- Dynamic inputs will be added here -->
                                        </div>
                                        <div class="error-container"></div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Debt Payment Section -->
        <div id="debt-payment-section" class="form-group" style="display: none;">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="debt-payment-checkbox">
                        <label class="form-check-label" for="debt-payment-checkbox">
                            <strong><?= $paymentTypes[WorkerFinances::TYPE_DEBT_PAYMENT] ?? 'Погашение долга' ?></strong>
                        </label>
                    </div>
                </div>
                <div class="card-body" id="debt-payment-details" style="display: none;">
                    <div class="row">
                        <div class="col-md-12">
                            <label><?= Yii::t('app', 'amount') ?></label>
                            <input type="text" class="form-control" id="debt-payment-amount"
                                   name="payment_types[<?= WorkerFinances::TYPE_DEBT_PAYMENT ?>][amount]"
                                   inputmode="numeric" pattern="[0-9\s]*" disabled>
                            <small class="text-muted"><?= Yii::t('app', 'debt_amount') ?>: <span id="worker-debt-amount">0</span></small>
                            <div class="error-container"></div>
                        </div>
                    </div>
                </div>
        </div>

    </form>
</div>

<script>
// Константы для JavaScript
const WORKER_FINANCES_TYPE_SALARY = 1;
const WORKER_FINANCES_TYPE_ADVANCE = 2;
const WORKER_FINANCES_TYPE_DEBT_PAYMENT = 6;
const WORKER_FINANCES_TYPE_CASH_SALARY = 8;
const WORKER_FINANCES_TYPE_CASH_ADVANCE = 9;
const WORKER_FINANCES_TYPE_CASH_BONUS = 10;
const WORKER_FINANCES_TYPE_CASH_ONE_TIME_PAYMENT = 11;
const WORKER_FINANCES_TYPE_CASH_VACATION_PAY = 12;
const PAYMENT_TYPE_CASH = 1;
const PAYMENT_TYPE_TRANSFER = 2;
const PAYMENT_TYPE_TERMINAL = 3;
const PAYMENT_TYPE_PAYMENT_CARD = 4;
</script>


