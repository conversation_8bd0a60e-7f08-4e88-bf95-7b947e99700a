a:50:{s:13:"68724bc1e4526";a:13:{s:3:"tag";s:13:"68724bc1e4526";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752320961.727846;s:10:"statusCode";i:200;s:8:"sqlCount";i:88;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11463024;s:14:"processingTime";d:0.34714603424072266;}s:13:"68724bcb195e8";a:13:{s:3:"tag";s:13:"68724bcb195e8";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752320970.850749;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10157472;s:14:"processingTime";d:0.29430317878723145;}s:13:"68724bcd0af63";a:13:{s:3:"tag";s:13:"68724bcd0af63";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752320972.810429;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9288088;s:14:"processingTime";d:0.28360486030578613;}s:13:"68724c3ebde29";a:13:{s:3:"tag";s:13:"68724c3ebde29";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321086.473242;s:10:"statusCode";i:200;s:8:"sqlCount";i:88;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11463648;s:14:"processingTime";d:0.5031239986419678;}s:13:"68724c403f301";a:13:{s:3:"tag";s:13:"68724c403f301";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321088.008358;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10157088;s:14:"processingTime";d:0.29930901527404785;}s:13:"68724cecc4302";a:13:{s:3:"tag";s:13:"68724cecc4302";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321260.527669;s:10:"statusCode";i:500;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10631832;s:14:"processingTime";d:0.8295550346374512;}s:13:"68724d83e0284";a:13:{s:3:"tag";s:13:"68724d83e0284";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321411.537685;s:10:"statusCode";i:500;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10615592;s:14:"processingTime";d:0.8920519351959229;}s:13:"68724dcfee90f";a:13:{s:3:"tag";s:13:"68724dcfee90f";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321487.688191;s:10:"statusCode";i:500;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10616520;s:14:"processingTime";d:0.7329840660095215;}s:13:"68724dff1c500";a:13:{s:3:"tag";s:13:"68724dff1c500";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321534.87153;s:10:"statusCode";i:500;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10609256;s:14:"processingTime";d:0.6317448616027832;}s:13:"68724e03ae61b";a:13:{s:3:"tag";s:13:"68724e03ae61b";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321539.495975;s:10:"statusCode";i:500;s:8:"sqlCount";i:5;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10610784;s:14:"processingTime";d:0.6174919605255127;}s:13:"68724e3548b3d";a:13:{s:3:"tag";s:13:"68724e3548b3d";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321589.044185;s:10:"statusCode";i:500;s:8:"sqlCount";i:6;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10725144;s:14:"processingTime";d:0.775763988494873;}s:13:"68724e7687085";a:13:{s:3:"tag";s:13:"68724e7687085";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321654.324467;s:10:"statusCode";i:500;s:8:"sqlCount";i:6;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10730768;s:14:"processingTime";d:0.6403388977050781;}s:13:"68724f703b365";a:13:{s:3:"tag";s:13:"68724f703b365";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321903.960723;s:10:"statusCode";i:200;s:8:"sqlCount";i:88;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11382968;s:14:"processingTime";d:0.45301103591918945;}s:13:"68724f75f372e";a:13:{s:3:"tag";s:13:"68724f75f372e";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321909.73908;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10134896;s:14:"processingTime";d:0.3076920509338379;}s:13:"68724f7ba8f67";a:13:{s:3:"tag";s:13:"68724f7ba8f67";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321915.371407;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9268096;s:14:"processingTime";d:0.420759916305542;}s:13:"68724f915d1e8";a:13:{s:3:"tag";s:13:"68724f915d1e8";s:3:"url";s:42:"http://silver/backend/worker-payment/store";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321937.152308;s:10:"statusCode";i:200;s:8:"sqlCount";i:65;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10179984;s:14:"processingTime";d:0.4010629653930664;}s:13:"68724f91d0249";a:13:{s:3:"tag";s:13:"68724f91d0249";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321937.613498;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10878296;s:14:"processingTime";d:0.3481299877166748;}s:13:"68724fa4abc30";a:13:{s:3:"tag";s:13:"68724fa4abc30";s:3:"url";s:36:"http://silver/backend/expenses/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321956.446049;s:10:"statusCode";i:200;s:8:"sqlCount";i:18;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10363984;s:14:"processingTime";d:0.3748178482055664;}s:13:"68724faa872ff";a:13:{s:3:"tag";s:13:"68724faa872ff";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752321962.271101;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11407160;s:14:"processingTime";d:0.469390869140625;}s:13:"6872509e2297d";a:13:{s:3:"tag";s:13:"6872509e2297d";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322205.829069;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11401768;s:14:"processingTime";d:0.4689521789550781;}s:13:"687250a0b7101";a:13:{s:3:"tag";s:13:"687250a0b7101";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322208.513389;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11402368;s:14:"processingTime";d:0.4038369655609131;}s:13:"687250b3c1723";a:13:{s:3:"tag";s:13:"687250b3c1723";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322227.52981;s:10:"statusCode";i:500;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10743224;s:14:"processingTime";d:0.8058769702911377;}s:13:"687250bc1aa84";a:13:{s:3:"tag";s:13:"687250bc1aa84";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322235.84838;s:10:"statusCode";i:500;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10744448;s:14:"processingTime";d:0.7031760215759277;}s:13:"6872511b33bd5";a:13:{s:3:"tag";s:13:"6872511b33bd5";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322330.957628;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11402904;s:14:"processingTime";d:0.38047003746032715;}s:13:"6872511e6a910";a:13:{s:3:"tag";s:13:"6872511e6a910";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322334.225738;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10386616;s:14:"processingTime";d:0.2767059803009033;}s:13:"6872526968e53";a:13:{s:3:"tag";s:13:"6872526968e53";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322665.169936;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11387592;s:14:"processingTime";d:0.3616499900817871;}s:13:"6872526b9fed6";a:13:{s:3:"tag";s:13:"6872526b9fed6";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322667.371267;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10131728;s:14:"processingTime";d:0.32366204261779785;}s:13:"6872526e72f1a";a:13:{s:3:"tag";s:13:"6872526e72f1a";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322670.250845;s:10:"statusCode";i:200;s:8:"sqlCount";i:16;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9312776;s:14:"processingTime";d:0.28072285652160645;}s:13:"68725274445ef";a:13:{s:3:"tag";s:13:"68725274445ef";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322676.035513;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11388192;s:14:"processingTime";d:0.3870718479156494;}i:6872528493299;a:13:{s:3:"tag";s:13:"6872528493299";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322692.39484;s:10:"statusCode";i:200;s:8:"sqlCount";i:88;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11366872;s:14:"processingTime";d:0.41357994079589844;}s:13:"687252864e4af";a:13:{s:3:"tag";s:13:"687252864e4af";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322693.988148;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10131728;s:14:"processingTime";d:0.36013007164001465;}s:13:"68725287e11b1";a:13:{s:3:"tag";s:13:"68725287e11b1";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322695.746414;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9264232;s:14:"processingTime";d:0.21358799934387207;}s:13:"68725295ec11c";a:13:{s:3:"tag";s:13:"68725295ec11c";s:3:"url";s:42:"http://silver/backend/worker-payment/store";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322709.711786;s:10:"statusCode";i:200;s:8:"sqlCount";i:66;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10182952;s:14:"processingTime";d:0.48770809173583984;}s:13:"68725296807a8";a:13:{s:3:"tag";s:13:"68725296807a8";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752322710.25795;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10867080;s:14:"processingTime";d:0.43390798568725586;}s:13:"687256dd253ea";a:13:{s:3:"tag";s:13:"687256dd253ea";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752323804.775622;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11419368;s:14:"processingTime";d:0.5414459705352783;}s:13:"687256df90dcb";a:13:{s:3:"tag";s:13:"687256df90dcb";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752323807.38234;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10146248;s:14:"processingTime";d:0.24781394004821777;}s:13:"6872575958aee";a:13:{s:3:"tag";s:13:"6872575958aee";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752323929.042993;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11418768;s:14:"processingTime";d:0.49611496925354004;}s:13:"687258c076035";a:13:{s:3:"tag";s:13:"687258c076035";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324288.127839;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11410112;s:14:"processingTime";d:0.47672390937805176;}s:13:"687258c25ba44";a:13:{s:3:"tag";s:13:"687258c25ba44";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324290.11905;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10139968;s:14:"processingTime";d:0.2899620532989502;}s:13:"687258dc525ee";a:13:{s:3:"tag";s:13:"687258dc525ee";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324316.06198;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11410112;s:14:"processingTime";d:0.4122941493988037;}s:13:"687258dd8e2c1";a:13:{s:3:"tag";s:13:"687258dd8e2c1";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324317.371369;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10139968;s:14:"processingTime";d:0.23205208778381348;}s:13:"687258e2db036";a:13:{s:3:"tag";s:13:"687258e2db036";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=87&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324322.661365;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9272440;s:14:"processingTime";d:0.2877171039581299;}s:13:"687258f2102c7";a:13:{s:3:"tag";s:13:"687258f2102c7";s:3:"url";s:42:"http://silver/backend/worker-payment/store";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324337.76919;s:10:"statusCode";i:200;s:8:"sqlCount";i:80;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10289672;s:14:"processingTime";d:0.5484910011291504;}s:13:"687258f298ada";a:13:{s:3:"tag";s:13:"687258f298ada";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324338.369058;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10883728;s:14:"processingTime";d:0.40373706817626953;}s:13:"68725928b49a7";a:13:{s:3:"tag";s:13:"68725928b49a7";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324392.485347;s:10:"statusCode";i:200;s:8:"sqlCount";i:88;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11381080;s:14:"processingTime";d:0.42767906188964844;}s:13:"6872592aed059";a:13:{s:3:"tag";s:13:"6872592aed059";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324394.716546;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10139968;s:14:"processingTime";d:0.30017709732055664;}s:13:"6872592db83dd";a:13:{s:3:"tag";s:13:"6872592db83dd";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=87&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324397.524039;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9272440;s:14:"processingTime";d:0.2945859432220459;}s:13:"6872593b0c18e";a:13:{s:3:"tag";s:13:"6872593b0c18e";s:3:"url";s:42:"http://silver/backend/worker-payment/store";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324410.76365;s:10:"statusCode";i:200;s:8:"sqlCount";i:64;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10178136;s:14:"processingTime";d:0.5070970058441162;}s:13:"6872593b96932";a:13:{s:3:"tag";s:13:"6872593b96932";s:3:"url";s:76:"http://silver/backend/worker-payment/index?_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324411.330557;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10881232;s:14:"processingTime";d:0.4430830478668213;}s:13:"687259b6c7658";a:13:{s:3:"tag";s:13:"687259b6c7658";s:3:"url";s:42:"http://silver/backend/worker-payment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752324534.496584;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11405976;s:14:"processingTime";d:0.5280499458312988;}}